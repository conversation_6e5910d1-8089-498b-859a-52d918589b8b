"""
聊天API模块

提供聊天相关的API接口。
"""

import json
import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from app.core.dependencies import get_chat_service, get_session_service, get_context_service
from app.services.chat_service import ChatService
from app.services.context_service import ContextService
from app.services.session_service import SessionService
from app.sse.response import SSEResponse

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/chat", tags=["chat"])



# 请求模型
class ChatRequest(BaseModel):
    """聊天请求模型"""
    session_id: str = Field(..., description="会话ID", example="123e4567-e89b-12d3-a456-************")
    message: str = Field(..., description="用户消息内容", example="我想在西二旗附近找房子")
    stream: bool = Field(False, description="是否使用流式响应", example=False)

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-************",
                "message": "我想在西二旗附近找房子",
                "stream": False
            }
        }

# 响应模型
class ChatResponse(BaseModel):
    """聊天响应模型"""
    session_id: str = Field(..., description="会话ID", example="123e4567-e89b-12d3-a456-************")
    response: str = Field(..., description="AI回复内容", example="好的，我会帮您在西二旗附近找房子。您有什么特殊的要求吗？比如预算、户型等。")
    intent: Optional[str] = Field(None, description="识别的意图", example="search_house")
    context: Optional[Dict[str, Any]] = Field(None, description="会话上下文", example={"location": "西二旗", "location_type": "商圈"})

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-************",
                "response": "好的，我会帮您在西二旗附近找房子。您有什么特殊的要求吗？比如预算、户型等。",
                "intent": "search_house",
                "context": {
                    "location": "西二旗",
                    "location_type": "商圈",
                    "state": "filtering_in_progress"
                }
            }
        }


@router.post("/stream", summary="流式发送消息(POST)", description="以流式方式发送消息给AI并获取回复")
async def stream_message_post(
    request: ChatRequest,
    http_request: Request,
    chat_service: ChatService = Depends(get_chat_service)
):
    """
    流式发送消息API（POST方法）

    使用工作流处理用户消息，以流式方式返回AI回复。返回的是Server-Sent Events (SSE)格式的流式响应。

    - **session_id**: 会话ID，用于标识对话上下文
    - **message**: 用户消息内容
    - **stream**: 此参数在此端点中无效，总是使用流式响应

    **示例请求**:
    ```json
    {
        "session_id": "123e4567-e89b-12d3-a456-************",
        "message": "我想在西二旗附近找房子"
    }
    ```

    **响应格式**:
    ```
    data: {"id":"uuid-1","parent_type":"requirement_communication","sub_type":"text","content":"好"}
    data: {"id":"uuid-2","parent_type":"requirement_communication","sub_type":"text","content":"的"}
    ...
    data: [DONE]
    ```

    其中:
    - **id**: 事件ID，每个事件唯一
    - **parent_type**: 父类型，表示业务场景，如"requirement_communication"表示需求沟通
    - **sub_type**: 子类型，表示数据格式，"text"表示文本内容，"json"表示JSON对象
    - **content**: 事件内容，当sub_type为"text"时是单个字符，当sub_type为"json"时是完整的JSON对象

    **可能的错误**:
    - 404: 会话不存在
    - 500: 服务器内部错误
    """

    # 处理消息
    try:
        # 从header中获取token
        token = http_request.headers.get("Authorization", "")
        if token.startswith("Bearer "):
            token = token[7:]  # 移除"Bearer "前缀
        elif token.startswith("Token "):
            token = token[6:]  # 移除"Token "前缀
        
        logger.info(f"从header获取token: {token[:20]}..." if token else "未获取到token")
        
        async def event_generator():
            # 获取完整的工作流上下文
            workflow_context = await chat_service.get_full_response_with_context(
                request.session_id, request.message, token=token
            )

            # 获取最新的会话信息（确保获取到最新状态）
            session_data = await chat_service.session_service.get_session(request.session_id)
            context = session_data.get("context", {})

            # 记录会话状态
            session_state = context.get("session_state", "idle")
            intent = context.get("intent", "")
            logger.info(f"响应后的会话状态: {session_state}, 意图: {intent}")

            # 优先使用新的WorkflowResponse，回退到旧的方法
            if hasattr(workflow_context, 'workflow_response') and workflow_context.workflow_response:
                logger.info("使用新的WorkflowResponse生成SSE响应")
                async for event in SSEResponse.generate_response_by_workflow_response(
                    workflow_context.workflow_response,
                    request.session_id
                ):
                    yield event
            else:
                logger.info("回退到旧的generate_response_by_context方法")
                # 获取响应文本
                response = workflow_context.response or ""

                # 记录响应类型
                try:
                    json_data = json.loads(response)
                    if isinstance(json_data, dict) and "data" in json_data:
                        logger.info("检测到JSON响应，可能是房源数据")
                except (json.JSONDecodeError, TypeError):
                    # 不是JSON响应
                    logger.info("检测到文本响应")

                # 使用 SSE 响应生成器
                async for event in SSEResponse.generate_response_by_workflow_response(
                    response,
                    context,
                    request.session_id
                ):
                    yield event

        # 返回流式响应
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream"
        )
    except ValueError as e:
        logger.error(f"Value error in stream_message_post: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error in stream_message_post: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing message: {str(e)}"
        )

@router.get("/stream", summary="流式发送消息(GET)", description="以流式方式发送消息给AI并获取回复（GET方法）")
async def stream_message_get(
    session_id: str = Query(..., description="会话ID", example="123e4567-e89b-12d3-a456-************"),
    message: str = Query(..., description="用户消息内容", example="我想在西二旗附近找房子"),
    http_request: Request = None,
    chat_service: ChatService = Depends(get_chat_service)
):
    """
    流式发送消息API（GET方法）

    使用工作流处理用户消息，以流式方式返回AI回复。返回的是Server-Sent Events (SSE)格式的流式响应。
    此端点功能与POST /stream相同，但使用GET方法，适用于某些不方便发送POST请求的场景。

    - **session_id**: 会话ID，用于标识对话上下文
    - **message**: 用户消息内容

    **示例请求**:
    ```
    GET /api/chat/stream?session_id=123e4567-e89b-12d3-a456-************&message=我想在西二旗附近找房子
    ```

    **响应格式**:
    ```
    data: {"id":"uuid-1","parent_type":"requirement_communication","sub_type":"text","content":"好"}
    data: {"id":"uuid-2","parent_type":"requirement_communication","sub_type":"text","content":"的"}
    ...
    data: [DONE]
    ```

    其中:
    - **id**: 事件ID，每个事件唯一
    - **parent_type**: 父类型，表示业务场景，如"requirement_communication"表示需求沟通
    - **sub_type**: 子类型，表示数据格式，"text"表示文本内容，"json"表示JSON对象
    - **content**: 事件内容，当sub_type为"text"时是单个字符，当sub_type为"json"时是完整的JSON对象

    **可能的错误**:
    - 404: 会话不存在
    - 500: 服务器内部错误
    """

    # 处理消息
    try:
        # 从header中获取token
        token = ""
        if http_request:
            token = http_request.headers.get("Authorization", "")
            if token.startswith("Bearer "):
                token = token[7:]  # 移除"Bearer "前缀
            elif token.startswith("Token "):
                token = token[6:]  # 移除"Token "前缀
        
        logger.info(f"从header获取token: {token[:20]}..." if token else "未获取到token")
        
        async def event_generator():
            # 获取完整的工作流上下文
            workflow_context = await chat_service.get_full_response_with_context(
                session_id, message, token=token
            )

            # 获取最新的会话信息（确保获取到最新状态）
            session_data = await chat_service.session_service.get_session(session_id)
            context = session_data.get("context", {})

            # 记录会话状态
            session_state = context.get("session_state", "idle")
            intent = context.get("intent", "")
            logger.info(f"响应后的会话状态: {session_state}, 意图: {intent}")

            # 优先使用新的WorkflowResponse，回退到旧的方法
            if hasattr(workflow_context, 'workflow_response') and workflow_context.workflow_response:
                logger.info("使用新的WorkflowResponse生成SSE响应")
                async for event in SSEResponse.generate_response_by_workflow_response(
                    workflow_context.workflow_response,
                    session_id
                ):
                    yield event
            else:
                logger.info("回退到旧的generate_response_by_context方法")
                # 获取响应文本
                response = workflow_context.response or ""

                # 记录响应类型
                try:
                    json_data = json.loads(response)
                    if isinstance(json_data, dict) and "data" in json_data:
                        logger.info("检测到JSON响应，可能是房源数据")
                except (json.JSONDecodeError, TypeError):
                    # 不是JSON响应
                    logger.info("检测到文本响应")

                # 使用 SSE 响应生成器
                async for event in SSEResponse.generate_response_by_workflow_response(
                    response,
                    context,
                    session_id
                ):
                    yield event

        # 返回流式响应
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream"
        )
    except ValueError as e:
        logger.error(f"Value error in stream_message_get: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error in stream_message_get: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing message: {str(e)}"
        )

@router.post("/clear", summary="清除会话", description="清除指定会话的上下文和消息历史")
async def clear_session(
    session_id: str = Query(..., description="会话ID", example="123e4567-e89b-12d3-a456-************"),
    session_service: SessionService = Depends(get_session_service),
    context_service: ContextService = Depends(get_context_service)
):
    """
    清除会话上下文API

    清除指定会话的上下文和消息历史，重置会话状态。这对于开始新的对话或解决上下文混乱的问题很有用。

    - **session_id**: 要清除的会话ID

    **示例请求**:
    ```
    POST /api/chat/clear?session_id=123e4567-e89b-12d3-a456-************
    ```

    **示例响应**:
    ```json
    {
        "session_id": "123e4567-e89b-12d3-a456-************",
        "status": "success",
        "message": "Session context and messages cleared successfully",
        "context": {}
    }
    ```

    **可能的错误**:
    - 404: 会话不存在
    - 500: 服务器内部错误
    """

    # 获取当前会话
    session = await session_service.get_session(session_id)
    if not session:
        logger.error(f"Session not found in clear_session: {session_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session not found: {session_id}"
        )

    try:
        # 获取当前上下文
        current_context = session["context"]

        # 记录清除前的上下文
        logger.info(f"Context before clearing (API): {json.dumps(current_context, ensure_ascii=False)}")

        # 初始化一个全新的上下文
        new_context = context_service.initialize_context()

        # 更新会话上下文
        await session_service.update_session_context(session_id, new_context)

        # 清除会话中的所有消息
        await session_service.clear_messages(session_id)

        # 记录清除后的上下文
        logger.info(f"Context after clearing (API): {json.dumps(new_context, ensure_ascii=False)}")

        return {
            "session_id": session_id,
            "status": "success",
            "message": "Session context and messages cleared successfully",
            "context": new_context
        }
    except Exception as e:
        logger.error(f"Error in clear_session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error clearing session: {str(e)}"
        )
