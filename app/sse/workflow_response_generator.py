"""
工作流响应SSE生成器

基于统一的WorkflowResponse类型生成SSE响应，消除复杂的判断逻辑。
"""

import json
import logging
from typing import AsyncGenerator, Dict, Any

from app.workflows.response_types import WorkflowResponse, ResponseType, ResponseFormat
from app.sse.generators import SSEGenerator
from app.sse.constants import ParentType, MappingRegistry, LOCATION_SUGGESTION_TITLE

logger = logging.getLogger(__name__)


class WorkflowResponseGenerator:
    """基于WorkflowResponse的SSE生成器"""
    
    @staticmethod
    async def generate_sse_stream(
        workflow_response: WorkflowResponse,
        session_id: str = ""
    ) -> AsyncGenerator[str, None]:
        """
        根据WorkflowResponse生成SSE流
        
        Args:
            workflow_response: 统一的工作流响应对象
            session_id: 会话ID
            
        Returns:
            SSE事件流
        """
        response_type = workflow_response.response_type
        content = workflow_response.content
        metadata = workflow_response.metadata
        
        logger.info(f"生成SSE流，响应类型: {response_type}, 格式: {workflow_response.format}")
        
        # 根据响应类型分发到不同的处理方法
        if response_type == ResponseType.TEXT:
            async for event in WorkflowResponseGenerator._generate_text_stream(
                workflow_response, session_id
            ):
                yield event
                
        elif response_type == ResponseType.HOUSE_LIST:
            async for event in WorkflowResponseGenerator._generate_house_list_stream(
                workflow_response, session_id
            ):
                yield event
                
        elif response_type == ResponseType.LOCATION_SUGGESTION:
            async for event in WorkflowResponseGenerator._generate_location_suggestion_stream(
                workflow_response, session_id
            ):
                yield event
                
        elif response_type == ResponseType.VIEWING_APPOINTMENT:
            async for event in WorkflowResponseGenerator._generate_viewing_appointment_stream(
                workflow_response, session_id
            ):
                yield event
                
        elif response_type == ResponseType.BIZCIRCLE_RECOMMENDATION:
            async for event in WorkflowResponseGenerator._generate_bizcircle_recommendation_stream(
                workflow_response, session_id
            ):
                yield event
                
        else:
            # 未知类型，回退到文本流
            logger.warning(f"未知响应类型: {response_type}，回退到文本流")
            async for event in WorkflowResponseGenerator._generate_fallback_text_stream(
                workflow_response, session_id
            ):
                yield event
    
    @staticmethod
    async def _generate_text_stream(
        workflow_response: WorkflowResponse,
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """生成文本流"""
        content = str(workflow_response.content)
        parent_type = workflow_response.get_sse_parent_type()
        delay = workflow_response.metadata.delay if workflow_response.metadata.requires_typewriter else 0
        
        logger.info(f"生成文本流，父类型: {parent_type}, 延迟: {delay}")
        
        async for event in SSEGenerator.generate_text_stream(
            content, parent_type, delay, session_id
        ):
            yield event
    
    @staticmethod
    async def _generate_house_list_stream(
        workflow_response: WorkflowResponse,
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """生成房源列表流"""
        content = workflow_response.content
        location_type = workflow_response.metadata.location_type
        parent_type = workflow_response.get_sse_parent_type(location_type)
        
        logger.info(f"生成房源列表流，位置类型: {location_type}, 父类型: {parent_type}")
        
        if not isinstance(content, dict):
            logger.error(f"房源列表内容格式错误: {type(content)}")
            return
        
        # 特殊处理行政区：生成商圈推荐Markdown
        if location_type == '行政区':
            markdown_text = WorkflowResponseGenerator._format_bizcircle_recommendation(content)
            async for event in SSEGenerator.generate_text_stream(
                markdown_text, parent_type, 0.05, session_id
            ):
                yield event
        else:
            # 其他类型：使用JSON数组流
            async for event in SSEGenerator.generate_json_array_stream(
                content, parent_type, session_id
            ):
                yield event
    
    @staticmethod
    async def _generate_location_suggestion_stream(
        workflow_response: WorkflowResponse,
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """生成位置建议流"""
        content = workflow_response.content
        parent_type = workflow_response.get_sse_parent_type()
        
        logger.info(f"生成位置建议流，父类型: {parent_type}")
        
        if not isinstance(content, dict):
            logger.error(f"位置建议内容格式错误: {type(content)}")
            return
        
        async for event in SSEGenerator.generate_json_array_stream(
            content, parent_type, session_id
        ):
            yield event
    
    @staticmethod
    async def _generate_viewing_appointment_stream(
        workflow_response: WorkflowResponse,
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """生成看房单流"""
        content = workflow_response.content
        
        logger.info("生成看房单流")
        
        if not isinstance(content, dict):
            logger.error(f"看房单内容格式错误: {type(content)}")
            return
        
        async for event in SSEGenerator.generate_viewing_appointment_stream(
            content, session_id
        ):
            yield event
    
    @staticmethod
    async def _generate_bizcircle_recommendation_stream(
        workflow_response: WorkflowResponse,
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """生成商圈推荐流"""
        content = workflow_response.content
        parent_type = workflow_response.get_sse_parent_type()
        
        logger.info(f"生成商圈推荐流，父类型: {parent_type}")
        
        if not isinstance(content, dict):
            logger.error(f"商圈推荐内容格式错误: {type(content)}")
            return
        
        # 转换为Markdown格式
        markdown_text = WorkflowResponseGenerator._format_bizcircle_recommendation(content)
        
        async for event in SSEGenerator.generate_text_stream(
            markdown_text, parent_type, 0.05, session_id
        ):
            yield event
    
    @staticmethod
    async def _generate_fallback_text_stream(
        workflow_response: WorkflowResponse,
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """回退文本流生成"""
        # 尝试将内容转换为字符串
        if isinstance(workflow_response.content, (dict, list)):
            content = json.dumps(workflow_response.content, ensure_ascii=False)
        else:
            content = str(workflow_response.content)
        
        parent_type = "requirement_communication"  # 默认父类型
        
        logger.info(f"回退文本流，内容长度: {len(content)}")
        
        async for event in SSEGenerator.generate_text_stream(
            content, parent_type, 0.05, session_id
        ):
            yield event
    
    @staticmethod
    def _format_bizcircle_recommendation(data: Dict[str, Any]) -> str:
        """格式化商圈推荐为Markdown"""
        result = ""
        for index, item in enumerate(data.get('data', [])):
            result += f"""
# {index + 1}. {item.get('bizcircle_name', '未知商圈')} - {item.get('description2', '暂无描述')}
## 商圈介绍:
- {item.get('highlight2', '暂无介绍')}

## 房源情况:"""

            # 处理房源产品信息
            if 'products' in item:
                shared_info, whole_info = WorkflowResponseGenerator._process_products(item['products'])
                if whole_info:
                    result += f"\n- 整租: {whole_info}"
                if shared_info:
                    result += f"\n- 合租: {shared_info}"
            else:
                # 使用基础价格信息
                result += f"\n- 价格区间: {item.get('min_price', '暂无')} - {item.get('max_price', '暂无')}元/月"
                result += f"\n- 房源数量: {item.get('house_count', '暂无')}套"

        return result
    
    @staticmethod
    def _process_products(products: list) -> tuple[str, str]:
        """处理产品信息，返回(合租信息, 整租信息)"""
        shared_count, shared_min_price = 0, 0
        whole_count, whole_min_price = 0, 0

        for product in products:
            if 'min_price' not in product:
                continue
                
            count = int(product.get('count', 0))
            price = float(product['min_price'])
            category = product.get('product_category')

            if category == "1":  # 合租
                shared_count += count
                shared_min_price = price if shared_min_price == 0 else min(shared_min_price, price)
            elif category in ("2", "3", "4", "5"):  # 整租
                whole_count += count
                whole_min_price = price if whole_min_price == 0 else min(whole_min_price, price)

        shared_info = f"{shared_count}间: {shared_min_price}/月起" if shared_count > 0 else ""
        whole_info = f"{whole_count}套: {whole_min_price}/月起" if whole_count > 0 else ""
        
        return shared_info, whole_info
