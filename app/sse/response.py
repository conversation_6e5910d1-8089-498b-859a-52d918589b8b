"""
SSE 响应处理模块

提供处理和生成 SSE 响应的功能。
"""

import json
import logging
from typing import AsyncGenerator, Dict, Any

from app.llm.prompts.extraction.dynamic import PARAMETER_DEFINITIONS
from app.sse.constants import ParentType, MappingRegistry, LOCATION_SUGGESTION_TITLE
from app.sse.generators import SSEGenerator
from app.sse.workflow_response_generator import WorkflowResponseGenerator
from app.workflows.response_types import WorkflowResponse

logger = logging.getLogger(__name__)


class SSEResponse:
    """SSE 响应处理类"""

    @staticmethod
    async def generate_response_by_workflow_response(
        workflow_response: WorkflowResponse,
        session_id: str = ""
    ) -> AsyncGenerator[str, None]:
        """
        基于WorkflowResponse生成SSE响应（推荐使用）

        Args:
            workflow_response: 统一的工作流响应对象
            session_id: 会话ID

        Returns:
            SSE事件流
        """
        logger.info(f"使用WorkflowResponse生成SSE响应，类型: {workflow_response.response_type}")

        async for event in WorkflowResponseGenerator.generate_sse_stream(
            workflow_response, session_id
        ):
            yield event

    @staticmethod
    async def generate_response_by_context(
        text: str,
        context: Dict[str, Any],
        session_id: str = "",
        delay: float = 0
    ) -> AsyncGenerator[str, None]:
        """
        根据上下文生成响应（向后兼容方法，推荐使用generate_response_by_workflow_response）

        Args:
            text: 响应文本
            context: 上下文信息，包含session_state、location_type等
            session_id: 会话 ID
            delay: 每个字符之间的延迟（秒）

        Returns:
            SSE 事件流
        """
        logger.warning("使用已废弃的generate_response_by_context方法，建议迁移到generate_response_by_workflow_response")
        session_state = context.get("session_state", "")
        location_type = context.get("location_type", "")

        try:
            json_data = json.loads(text)
            # 1. 检测看房单数据
            if (isinstance(json_data, dict) and 
                json_data.get("parent_type") == "view_house" and 
                "data" in json_data):
                logger.info(f"检测到看房单数据，数据块数量: {len(json_data.get('data', []))}")
                async for event in SSEGenerator.generate_viewing_appointment_stream(json_data, session_id):
                    yield event
                return
            # 2. 检测位置建议数据
            elif (isinstance(json_data, dict) and 
                "data" in json_data and 
                "title" in json_data and 
                json_data.get("title") == LOCATION_SUGGESTION_TITLE):
                logger.info(f"检测到位置建议数据，建议数量: {len(json_data.get('data', []))}")
                async for event in SSEResponse._handle_location_suggestion_response(json_data, session_id):
                    yield event
                return
        except (json.JSONDecodeError, TypeError):
            # 不是JSON或者不是位置建议格式，继续后续处理
            pass

        # 2. 需求沟通阶段：直接返回文本流
        if SSEResponse._is_requirement_communication(session_state):
            logger.info(f"需求沟通阶段: {session_state}")
            async for event in SSEGenerator.generate_text_stream(text, ParentType.TypeRequirementCommunication, delay, session_id):
                yield event
            return

        # 3. 浏览阶段：处理房源数据
        if session_state == "browsing":
            logger.info(f"浏览阶段，位置类型: {location_type}")
            async for event in SSEResponse._handle_browsing_response(text, location_type, session_id):
                yield event
            return

        # 4. 默认情况：文本流
        logger.info(f"默认处理，会话状态: {session_state}")
        async for event in SSEGenerator.generate_text_stream(text, ParentType.TypeRequirementCommunication, delay, session_id):
            yield event

    @staticmethod
    def _is_requirement_communication(session_state: str) -> bool:
        """判断是否为需求沟通阶段"""
        return session_state in ["asking_location", "asking_budget", "asking_room_type"]

    @staticmethod
    async def _handle_browsing_response(text: str, location_type: str, session_id: str) -> AsyncGenerator[str, None]:
        """处理浏览阶段的响应"""
        parent_type = SSEResponse._get_parent_type_by_location_type(location_type)
        
        # 尝试解析JSON数据
        try:
            json_data = json.loads(text)
            if isinstance(json_data, dict) and "title" in json_data and "data" in json_data:
                logger.info(f"检测到房源JSON数据，房源数量: {len(json_data['data'])}")
                
                # 行政区特殊处理：生成商圈推荐
                if location_type == '行政区':
                    markdown_text = SSEResponse._format_bizcircle_recommendation(json_data)
                    async for event in SSEGenerator.generate_text_stream(markdown_text, parent_type, 0.05, session_id):
                        yield event
                else:
                    # 其他类型：使用JSON数组流
                    async for event in SSEGenerator.generate_json_array_stream(json_data, parent_type, session_id):
                        yield event
                return
        except (json.JSONDecodeError, TypeError) as e:
            logger.info(f"JSON解析失败: {str(e)}，使用文本流")

        # JSON解析失败，使用文本流
        async for event in SSEGenerator.generate_text_stream(text, parent_type, 0, session_id):
            yield event

    @staticmethod
    async def _handle_location_suggestion_response(data: Dict[str, Any], session_id: str) -> AsyncGenerator[str, None]:
        """处理位置建议响应"""
        logger.info("处理位置建议响应")
        
        # 使用位置建议类型生成JSON数组流
        async for event in SSEGenerator.generate_json_array_stream(
            data, 
            ParentType.TypeLocationSuggestion, 
            session_id
        ):
            yield event

    @staticmethod
    def _format_bizcircle_recommendation(data: Dict[str, Any]) -> str:
        """格式化商圈推荐为Markdown"""
        result = ""
        for index, item in enumerate(data['data']):
            result += f"""
# {index + 1}. {item['bizcircle_name']} - {item['description2']}
## 商圈介绍:
- {item['highlight2']}

## 房源情况:"""

            # 处理房源产品信息
            if 'products' in item:
                shared_info, whole_info = SSEResponse._process_products(item['products'])
                if whole_info:
                    result += f"\n- 整租: {whole_info}"
                if shared_info:
                    result += f"\n- 合租: {shared_info}"
            else:
                # 使用基础价格信息
                result += f"\n- 价格区间: {item.get('min_price', '暂无')} - {item.get('max_price', '暂无')}元/月"
                result += f"\n- 房源数量: {item.get('house_count', '暂无')}套"

        return result

    @staticmethod
    def _process_products(products: list) -> tuple[str, str]:
        """处理产品信息，返回(合租信息, 整租信息)"""
        shared_count, shared_min_price = 0, 0
        whole_count, whole_min_price = 0, 0

        for product in products:
            if 'min_price' not in product:
                continue
                
            count = int(product.get('count', 0))
            price = float(product['min_price'])
            category = product.get('product_category')

            if category == "1":  # 合租
                shared_count += count
                shared_min_price = price if shared_min_price == 0 else min(shared_min_price, price)
            elif category in ("2", "3", "4", "5"):  # 整租
                whole_count += count
                whole_min_price = price if whole_min_price == 0 else min(whole_min_price, price)

        shared_info = f"{shared_count}间: {shared_min_price}/月起" if shared_count > 0 else ""
        whole_info = f"{whole_count}套: {whole_min_price}/月起" if whole_count > 0 else ""
        
        return shared_info, whole_info

    @staticmethod
    def _get_parent_type_by_location_type(location_type: str) -> str:
        """
        根据位置类型获取父类型

        Args:
            location_type: 位置类型

        Returns:
            对应的父类型
        """
        return MappingRegistry.get_parent_type_by_location_type(location_type)

    @staticmethod
    async def generate_viewing_appointment_response(
        text: str,
        session_id: str = "",
        delay: float = 0.05
    ) -> AsyncGenerator[str, None]:
        """
        生成看房单预约响应

        Args:
            text: 响应文本
            session_id: 会话 ID
            delay: 每个字符之间的延迟（秒）

        Returns:
            SSE 事件流
        """
        logger.info(f"生成看房单预约响应，文本长度: {len(text)}")
        
        # 看房单响应使用文本流形式，带有打字机效果
        async for event in SSEGenerator.generate_text_stream(
            text, 
            ParentType.TypeViewHouse, 
            delay, 
            session_id
        ):
            yield event

    @staticmethod
    async def generate_viewing_appointment_json_response(
        viewing_data: Dict[str, Any],
        session_id: str = ""
    ) -> AsyncGenerator[str, None]:
        """
        生成看房单预约的JSON响应

        Args:
            viewing_data: 看房单数据，包含预约确认信息
            session_id: 会话 ID

        Returns:
            SSE 事件流
        """
        logger.info(f"生成看房单JSON响应，数据: {viewing_data}")
        
        # 看房单数据使用专门的生成器
        async for event in SSEGenerator.generate_viewing_appointment_stream(
            viewing_data, 
            session_id
        ):
            yield event

    

