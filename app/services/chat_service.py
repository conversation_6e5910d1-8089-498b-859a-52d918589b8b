"""
聊天服务模块

提供处理用户消息并生成回复的服务。
"""

import logging
from typing import Dict, Any, AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession

from app.workflows.factory import WorkflowFactory
from app.services.session_service import SessionService

logger = logging.getLogger(__name__)

class ChatService:
    """
    聊天服务

    负责处理用户消息并生成回复。使用工作流模式组织业务逻辑，
    将复杂的处理流程分解为一系列可组合的步骤。
    """

    def __init__(
        self,
        db: AsyncSession,
        session_service: SessionService,
        workflow_factory: WorkflowFactory
    ):
        """
        初始化聊天服务

        接受所有依赖作为构造函数参数，符合依赖注入原则。

        Args:
            db: 数据库会话
            session_service: 会话服务
            workflow_factory: 工作流工厂
        """
        self.db = db
        self.session_service = session_service
        self.workflow_factory = workflow_factory

    async def process_message(
        self, session_id: str, user_message: str
    ) -> Dict[str, Any]:
        """
        处理用户消息

        使用工作流处理用户消息。

        Args:
            session_id: 会话ID
            user_message: 用户消息内容

        Returns:
            处理结果，包含AI回复和更新的参数
        """
        logger.info(f"Processing user message for session {session_id}: {user_message}")

        # 使用工作流工厂处理消息
        result = await self.workflow_factory.process_message(
            self.db, session_id, user_message, self.session_service
        )

        # 构建响应
        response = {
            "session_id": session_id,
            "intent": result.get("intent"),
            "confidence": result.get("confidence", 0.0),
            "extracted_params": result.get("extracted_params", {}),
            "context": result.get("updated_context", {}),
            "search_result": result.get("search_result", {"error": "No search results available"}),
            "prompt_version": result.get("prompt_version", "standard"),
            "response": result.get("response", "")
        }

        return response

    async def get_full_response(self, session_id: str, user_message: str, token: str = "") -> str:
        """
        获取完整响应

        使用工作流处理用户消息，并返回完整响应。
        注意：此方法不会保存响应到会话历史，这个责任由工作流中的SaveAssistantMessageStep承担。

        Args:
            session_id: 会话ID
            user_message: 用户消息内容
            token: 用户认证token

        Returns:
            完整的AI回复
        """
        # 使用工作流工厂处理消息，传递token
        result = await self.workflow_factory.process_message(
            self.db, session_id, user_message, self.session_service, token=token
        )

        # 获取响应
        response = result.get("response")
        if not response:
            logger.error("No response available")
            return "抱歉，处理您的请求时出现了问题。"

        # 记录完整响应
        logger.info(f"完整响应: {response}")

        # 不再在这里保存响应，而是完全依赖工作流中的SaveAssistantMessageStep
        # 这样可以避免重复保存消息

        return response

