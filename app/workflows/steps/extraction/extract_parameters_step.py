"""
参数提取步骤模块

提供从用户输入中提取参数的工作流步骤。
"""

import logging

from app.llm.extraction.extractor import ParameterExtractor
from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext

logger = logging.getLogger(__name__)


class ExtractParametersStep(WorkflowStep):
    """
    提取参数步骤

    从用户输入中提取参数，包括位置、预算、户型等。
    使用LLM进行参数提取，支持各种表达方式和同义词。
    """

    def __init__(self, parameter_extractor: ParameterExtractor):
        """
        初始化提取参数步骤

        Args:
            parameter_extractor: 参数提取器，用于从用户输入中提取参数
        """
        super().__init__("提取参数")
        self.parameter_extractor = parameter_extractor

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        从用户消息中提取参数

        Args:
            context: 工作流上下文，包含用户消息、意图和对话历史

        Returns:
            更新了提取参数的上下文
        """
        # 获取必要信息
        user_message = context.user_message
        history = context.history
        current_context = context.updated_context
        if not current_context and context.session and "context" in context.session:
            current_context = context.session["context"]

        # 检查是否是纯分页请求（只包含分页关键词且长度很短）
        pagination_keywords = ["再来", "更多", "下一页", "继续", "还有", "其他", "接着", "后面"]
        is_pure_pagination = any(keyword in user_message for keyword in pagination_keywords) and len(user_message.strip()) < 10

        # 如果是纯分页请求，直接处理分页逻辑，不调用LLM提取参数
        if is_pure_pagination:
            self.logger.info("检测到纯分页请求，递增分页数")
            # 递增分页数
            current_page = current_context.get("current_page", 1)
            new_page = current_page + 1
            # 更新上下文中的分页参数
            current_context["current_page"] = new_page
            # 设置意图为继续搜索，确保执行搜索房源步骤
            context.intent = "continue_search"
            context.confidence = 1.0
            # 设置分页标志
            current_context["pagination"] = "true"
            self.logger.info(f"分页数递增为: {new_page}, 意图设置为: {context.intent}")

            # 确保分页参数被正确地传递到会话上下文中
            if context.session and "context" in context.session:
                session_context = context.session["context"]
                session_context["current_page"] = new_page
                session_context["pagination"] = "true"
                self.logger.info(f"更新会话上下文中的分页参数: current_page={new_page}")

            # 对于纯分页请求，不调用LLM提取参数，但需要将分页参数添加到提取的参数中
            # 这样，分页参数会被正确地合并到会话上下文中
            context.extracted_params = {
                "current_page": new_page,
                "pagination": "true"
            }
            self.logger.info(f"设置提取的参数: current_page={new_page}, pagination=true")
            return context

        # 获取参数类型 - 不限制特定类型，提取所有可能的参数
        parameter_types = getattr(context, "parameter_types", None)

        # 提取参数
        extracted_params = await self.parameter_extractor.extract_parameters(
            user_message,
            history,
            current_context,
            parameter_types=parameter_types
        )

        # 更新上下文
        context.extracted_params = extracted_params

        # 记录提取的参数
        self.logger.info(f"提取的参数: {extracted_params}")

        # 记录所有参数
        for key, value in extracted_params.items():
            if value and value != "null":
                self.logger.info(f"{key}参数: {value}")

        return context

    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤

        只有在找房相关意图时才执行参数提取。
        找房相关意图包括：search_house, continue_search, community_inquiry

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        # 检查意图是否已识别
        if not hasattr(context, 'intent') or not context.intent:
            return False

        intent = context.intent

        # 只有找房相关意图才执行参数提取
        housing_related_intents = ["search_house", "continue_search", "community_inquiry"]
        should_execute = intent in housing_related_intents

        # 对于继续搜索意图，记录特殊日志
        if intent == "continue_search":
            self.logger.info("检测到继续搜索意图，执行参数提取以获取可能的新条件")
        elif intent == "community_inquiry":
            self.logger.info("检测到小区咨询意图，执行参数提取以获取位置信息")
        elif not should_execute:
            self.logger.info(f"意图不是找房相关 ({intent})，跳过参数提取")

        return should_execute
