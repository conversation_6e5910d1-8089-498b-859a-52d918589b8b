"""
位置处理步骤模块

提供处理位置参数的工作流步骤。
"""

import logging
import json

from app.services.bff.location_service import LocationService
from app.services.session_service import SessionService
from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.sse.constants import LOCATION_SUGGESTION_TITLE
from app.workflows.response_types import create_location_suggestion_response

logger = logging.getLogger(__name__)


class ProcessLocationStep(WorkflowStep):
    """
    处理位置步骤

    处理用户输入中的位置参数，包括：
    1. 获取位置建议
    2. 判断是否完全匹配
    3. 如果完全匹配，自动更新位置和位置类型
    4. 如果不完全匹配，返回建议列表供用户选择
    """

    def __init__(self, session_service: SessionService):
        """
        初始化处理位置步骤
        
        Args:
            session_service: 会话服务，用于保存消息和更新会话上下文
        """
        super().__init__("处理位置")
        self.location_service = LocationService()
        self.session_service = session_service
        self.logger = logging.getLogger(__name__)

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        处理位置参数

        Args:
            context: 工作流上下文，包含提取的参数

        Returns:
            更新了位置信息的上下文，或设置了提前返回的上下文
        """
        # 检查是否有提取的位置参数
        if not context.extracted_params:
            self.logger.info("没有提取到参数，跳过位置处理")
            return context

        # 获取位置参数
        extracted_params = context.extracted_params
        if "location" not in extracted_params or not extracted_params["location"]:
            self.logger.info("没有提取到位置参数，跳过位置处理")
            return context

        # 获取位置参数
        location = extracted_params["location"]
        self.logger.info(f"处理位置参数: {location}")

        # 获取位置建议
        location_suggestions = await self.location_service.get_location_suggestions(location)

        if not location_suggestions:
            self.logger.info(f"没有找到位置建议: {location}")
            return context

        # 处理位置输入 - 使用提取出的位置参数而不是整个用户消息
        result = self.location_service.process_location(location, location_suggestions)

        # 判断返回类型并处理
        if isinstance(result, tuple):
            # 完全匹配情况，直接更新上下文
            location, location_type = result  # 注意：process_location返回的是(location, location_type)
            self.logger.info(f"位置完全匹配: {location} (类型: {location_type})")

            # 更新提取的参数
            self.logger.info(f"更新前的extracted_params: {extracted_params}")
            extracted_params["location"] = location
            extracted_params["location_type"] = location_type
            self.logger.info(f"更新后的extracted_params: {extracted_params}")

            # 如果已经有更新的上下文，也更新它
            if context.updated_context is not None:
                self.logger.info(f"更新前的updated_context: {context.updated_context}")
                context.updated_context["location"] = location
                context.updated_context["location_type"] = location_type
                self.logger.info(f"更新后的updated_context: {context.updated_context}")
            else:
                self.logger.info("updated_context不存在，只更新extracted_params")
        else:
            # 不完全匹配情况，设置提前返回，返回位置建议列表
            self.logger.info(f"位置不完全匹配，返回{len(result)}个建议供用户选择")

            # 构建位置建议响应数据
            # 在每个建议项中添加highlight字段
            enhanced_result = []
            for item in result:
                enhanced_item = item.copy()  # 复制原有数据
                enhanced_item["highlight"] = location  # 添加提取出的原始位置文本
                enhanced_result.append(enhanced_item)
            
            suggestions_data = {
                "title": LOCATION_SUGGESTION_TITLE,
                "data": enhanced_result  # 使用增强后的建议列表数据
            }

            # 创建统一的工作流响应
            context.workflow_response = create_location_suggestion_response(suggestions_data)

            # 保持向后兼容
            context.response = json.dumps(suggestions_data, ensure_ascii=False)
            
            # 保存assistant消息
            session_id = context.session_id
            if context.response:
                # 使用注入的会话服务保存消息
                await self.session_service.add_message(
                    session_id=session_id,
                    role="assistant",
                    content=context.response
                )
                self.logger.info(f"✅ ProcessLocationStep：已保存assistant消息到数据库")
            
            # 设置提前返回标志
            context.early_return = True

        return context

    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤

        只有在找房相关意图且有提取的参数且包含位置信息时才执行。
        找房相关意图包括：search_house, continue_search, community_inquiry

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        # 检查意图是否已识别且是找房相关意图
        if not context.intent:
            return False

        intent = context.intent
        housing_related_intents = ["search_house", "continue_search", "community_inquiry"]
        if intent not in housing_related_intents:
            self.logger.info(f"意图不是找房相关 ({intent})，跳过位置处理")
            return False

        # 检查是否有提取的参数
        if not context.extracted_params:
            return False

        # 检查是否有位置参数
        extracted_params = context.extracted_params
        has_location = "location" in extracted_params and extracted_params["location"]

        if has_location:
            self.logger.info(f"执行位置处理: {extracted_params['location']}")
        else:
            self.logger.info("没有提取到位置参数，跳过位置处理")

        return has_location
