"""
预约看房意图处理步骤模块

专门处理预约看房意图的工作流步骤，包括：
1. 收集看房房源（房源识别和确认）
2. 收集看房时间（时间提取和验证）
3. 收集其他备注信息
4. 创建看房单
"""

import logging
import json

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.llm.extraction.extractor import ParameterExtractor
from app.llm.client import generate_completion
from app.utils.time_parser import TimeParser
from app.services.bff.reserve_service import ReservationService
from app.services.session_service import SessionService
from app.workflows.response_types import create_viewing_appointment_response, create_text_response

logger = logging.getLogger(__name__)


class ProcessViewingAppointmentStep(WorkflowStep):
    """
    处理预约看房意图步骤

    专门处理意图为"viewing_appointment"的逻辑：
    1. 识别和确认看房房源
    2. 收集和验证看房时间
    3. 收集其他备注信息
    4. 创建看房单
    """

    def __init__(self, parameter_extractor: ParameterExtractor, session_service: SessionService):
        """
        初始化预约看房处理步骤

        Args:
            parameter_extractor: 参数提取器，用于提取参数
            session_service: 会话服务，用于保存消息
        """
        super().__init__("处理预约看房")
        self.parameter_extractor = parameter_extractor
        self.session_service = session_service
        self.reservation_service = ReservationService()

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        处理预约看房意图

        Args:
            context: 工作流上下文

        Returns:
            更新了响应的上下文
        """
        user_message = context.user_message
        history = context.history
        current_context = context.updated_context or {}

        self.logger.info(f"开始处理预约看房意图，用户消息: {user_message}")
        
        # 第一步：识别和确认看房房源
        target_house = await self._identify_target_house(user_message, current_context)
        
        if not target_house:
            # 没有明确的房源目标，询问用户具体想看哪套
            house_list = current_context.get("house_list", [])
            if len(house_list) == 0:
                response = "请您先告诉我想看哪个小区的房源，我为您搜索后再安排看房。"
            elif len(house_list) == 1:
                # 只有一套房源，直接确认
                target_house = house_list[0]
                self.logger.info(f"对话中只有一套房源，默认为看房目标: {target_house.get('name_v2', '房源')}")
            else:
                # 多套房源，询问具体是哪套
                response = "请问是上面具体哪一套房源呢，您可以告诉小木具体的价格或者是第几套。"

                # 创建统一的工作流响应
                context.workflow_response = create_text_response(
                    content=response,
                    session_state="viewing_appointment_clarify",
                    delay=0.05,
                    requires_typewriter=True
                )

                # 保持向后兼容
                context.response = response
                await self.session_service.add_message(
                    session_id=context.session_id,
                    role="assistant",
                    content=context.response
                )
                self.logger.info(f"✅ ProcessViewingAppointmentStep(询问具体房源)：已保存assistant消息到数据库")
                context.early_return = True
                return context
        
        if not target_house:
            # 创建统一的工作流响应
            context.workflow_response = create_text_response(
                content=response,
                session_state="viewing_appointment_no_house",
                delay=0.05,
                requires_typewriter=True
            )

            # 保持向后兼容
            context.response = response
            await self.session_service.add_message(
                session_id=context.session_id,
                role="assistant",
                content=context.response
            )
            self.logger.info(f"✅ ProcessViewingAppointmentStep(询问房源)：已保存assistant消息到数据库")
            context.early_return = True
            return context
        
        # 第二步：提取和验证看房时间
        extracted_params = await self.parameter_extractor.extract_parameters(
            user_message,
            history,
            current_context,
            parameter_types={"viewing_time"}  # 只提取时间参数
        )

        viewing_time = extracted_params.get("viewing_time")
        self.logger.info(f"提取的看房时间: {viewing_time}")

        # 检查是否包含时间信息
        if not viewing_time or viewing_time == "null":
            # 没有时间信息，询问时间
            response = "您可以告诉我具体想参观哪套房源（可以告诉我，小区+价格，或者上面第几套），以及您方便的看房时间段，我们的工作时间是早上 9 点至晚上 9 点，可根据您的时间灵活预约。"
            self.logger.info("用户未提供看房时间，生成询问时间的回复")

            # 创建统一的工作流响应
            context.workflow_response = create_text_response(
                content=response,
                session_state="viewing_appointment_ask_time",
                delay=0.05,
                requires_typewriter=True
            )

            # 保持向后兼容
            context.response = response
            await self.session_service.add_message(
                session_id=context.session_id,
                role="assistant",
                content=context.response
            )
            self.logger.info(f"✅ ProcessViewingAppointmentStep(询问时间)：已保存assistant消息到数据库")
            context.early_return = True
            return context
        
        # 第三步：验证时间是否在工作时间范围内
        parsed_datetime_str = TimeParser.parse_viewing_time(viewing_time)
        
        if parsed_datetime_str:
            try:
                # 将字符串转换为datetime对象
                from datetime import datetime
                parsed_datetime = datetime.strptime(parsed_datetime_str, "%Y-%m-%d %H:%M:%S")
                
                # 验证时间是否在工作时间内（9点-21点）
                hour = parsed_datetime.hour
                if hour < 9 or hour >= 21:
                    response = "我们的工作时间是早上 9 点至晚上 9 点，您可以选择方便的时间预约。"
                    context.response = response
                    await self.session_service.add_message(
                        session_id=context.session_id,
                        role="assistant",
                        content=context.response
                    )
                    self.logger.info(f"✅ ProcessViewingAppointmentStep(时间超出工作时间)：已保存assistant消息到数据库")
                    context.early_return = True
                    return context
                
                # 格式化显示时间
                display_time = TimeParser.format_viewing_time_display(parsed_datetime_str)
                self.logger.info(f"解析的看房时间: {parsed_datetime}, 显示为: {display_time}")
                
                # 第四步：创建看房单
                house_list = [target_house] if target_house else []
                house_list_formatted = self._convert_house_list_for_reservation({"house_list": house_list})
                
                try:
                    # 将datetime对象转换为字符串格式（用于API调用）
                    expect_time_str = parsed_datetime.strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 调用create_reservation接口
                    reservation_result = await self.reservation_service.create_reservation(
                        token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwOWQ0M2M3MS01OGRmLTRlMzYtODhlZi1mNjFiODI2NDUxODciLCJ0eXBlIjoxLCJsZW5ndGgiOjQzMjAwLCJ0b2tlbiI6IjdiNTBkNmY1LTE3NTAtNDk0Mi1hMTQ4LWQ4ZTE0YWNmNGVhNSIsImNyZWF0ZVRpbWUiOjE3NDkxMDMyNjEzMjZ9.9KTbYmN_s7pXP7kOWnoyI9OYUccmAKbOk5619HuZoBI",
                        expect_time=expect_time_str,
                        house_list=house_list_formatted
                    )
                    
                    self.logger.info(f"创建看房单结果: {reservation_result}")
                    
                    # 检查预约结果
                    if reservation_result.get("code") == 200:
                        # 预约成功，构造SSE格式的看房单数据
                        sse_response_data = self._build_viewing_sse_response(
                            reservation_result,
                            viewing_time=viewing_time,
                            parsed_datetime=parsed_datetime,
                            display_time=display_time
                        )

                        # 创建统一的工作流响应
                        context.workflow_response = create_viewing_appointment_response(sse_response_data)

                        # 保持向后兼容
                        context.response = json.dumps(sse_response_data, ensure_ascii=False)

                        # 设置标志，表示这是看房单数据，需要特殊的SSE处理
                        context.updated_context["is_viewing_appointment"] = True
                        
                        # 记录预约信息到上下文
                        context.updated_context = {
                            **current_context,
                            "viewing_time": viewing_time,           # 原始时间文本
                            "viewing_datetime": parsed_datetime,    # 解析后的标准时间，供API使用
                            "viewing_display_time": display_time,   # 用户友好的显示时间
                            "target_house": target_house,           # 目标房源
                            "reservation_result": reservation_result  # 预约结果
                        }
                    else:
                        # 预约失败
                        error_msg = reservation_result.get("message", "预约失败")
                        self.logger.error(f"创建看房单失败: {error_msg}")
                        response = f"抱歉，预约失败：{error_msg}。请稍后再试或联系客服。"
                        
                        context.response = response
                        await self.session_service.add_message(
                            session_id=context.session_id,
                            role="assistant",
                            content=context.response
                        )
                        self.logger.info(f"✅ ProcessViewingAppointmentStep(预约失败)：已保存assistant消息到数据库")
                        context.early_return = True
                        return context
                        
                except Exception as e:
                    # 预约接口调用异常
                    self.logger.exception(f"调用预约接口异常: {str(e)}")
                    response = "抱歉，系统繁忙，请稍后再试。"
                    context.response = response
                    await self.session_service.add_message(
                        session_id=context.session_id,
                        role="assistant",
                        content=context.response
                    )
                    self.logger.info(f"✅ ProcessViewingAppointmentStep(异常)：已保存assistant消息到数据库")
                    context.early_return = True
                    return context
                    
            except ValueError as e:
                # 时间格式转换失败
                self.logger.error(f"时间格式转换失败: {parsed_datetime_str}, 错误: {str(e)}")
                response = "抱歉，时间格式有误。请您具体说明看房时间，比如'明天下午3点'或'周六上午10点'。我们的工作时间是早上 9 点至晚上 9 点。"
                
                context.response = response
                await self.session_service.add_message(
                    session_id=context.session_id,
                    role="assistant",
                    content=context.response
                )
                self.logger.info(f"✅ ProcessViewingAppointmentStep(时间格式转换失败)：已保存assistant消息到数据库")
                context.early_return = True
                return context
        
        else:
            # 时间解析失败
            self.logger.warning(f"时间解析失败: {viewing_time}")
            response = "抱歉，我没有理解您的时间安排。请您具体说明看房时间，比如'明天下午3点'或'周六上午10点'。我们的工作时间是早上 9 点至晚上 9 点。"
            
            context.response = response
            await self.session_service.add_message(
                session_id=context.session_id,
                role="assistant",
                content=context.response
            )
            self.logger.info(f"✅ ProcessViewingAppointmentStep(时间解析失败)：已保存assistant消息到数据库")
            context.early_return = True
            return context
        
        self.logger.info("生成预约确认回复")
        
        # 保存assistant消息到数据库 
        await self.session_service.add_message(
            session_id=context.session_id,
            role="assistant",
            content=context.response
        )
        self.logger.info(f"✅ ProcessViewingAppointmentStep(预约成功)：已保存assistant消息到数据库")
        context.early_return = True

        return context

    async def _identify_target_house(self, user_message: str, context: dict) -> dict:
        """
        使用LLM智能识别用户想要看房的目标房源

        Args:
            user_message: 用户消息
            context: 上下文

        Returns:
            目标房源对象，如果无法确定则返回None
        """
        house_list = context.get("house_list", [])

        self.logger.info(f"house_list_detail is {house_list}")

        if not house_list:
            self.logger.info("没有可用的房源列表")
            return None

        # 如果只有一套房源，直接返回
        if len(house_list) == 1:
            self.logger.info("只有一套房源，直接选择")
            return house_list[0]

        # 多套房源时，使用LLM进行智能识别
        try:
            target_house = await self._llm_identify_house(user_message, house_list)
            if target_house:
                self.logger.info(f"LLM成功识别目标房源: {target_house.get('resblock_name', '')} - {target_house.get('price', '')}元")
                return target_house
        except Exception as e:
            self.logger.error(f"LLM识别房源失败: {str(e)}")

        # LLM识别失败，回退到简单规则匹配
        fallback_house = self._fallback_identify_house(user_message, house_list)
        if fallback_house:
            self.logger.info(f"回退规则识别成功: {fallback_house.get('resblock_name', '')} - {fallback_house.get('price', '')}元")
            return fallback_house

        # 无法识别，返回None
        self.logger.info("无法识别目标房源")
        return None

    async def _llm_identify_house(self, user_message: str, house_list: list) -> dict:
        """
        使用LLM智能识别用户指定的房源

        Args:
            user_message: 用户消息
            house_list: 房源列表

        Returns:
            识别到的房源对象，如果无法识别则返回None
        """
        # 构建房源信息摘要（基于实际的house_list结构）
        house_summaries = []
        for i, house in enumerate(house_list):
            name = house.get('name', '未知房源')
            summary = f"第{i+1}套: {name}"
            house_summaries.append(summary)

        # 构建LLM提示词
        prompt = f"""你是一个房源识别助手。用户想要预约看房，请根据用户的描述识别他们想要预约的具体房源。

可选房源列表：
{chr(10).join(house_summaries)}

用户描述："{user_message}"

请分析用户的描述，识别他们想要预约的房源。用户可能会说：
- "第一套"、"第二套"、"第三套"等序号
- "3居室"、"2居室"、"4居室"等户型描述
- "01卧"、"02卧"、"03卧"等卧室编号
- "这套"、"那套"、"上面那套"等指代词
- 房源名称中的关键词

请只返回房源的序号（1、2、3等），如果无法确定请返回"0"。

房源序号："""

        try:
            # 准备消息格式
            messages = [
                {"role": "user", "content": prompt}
            ]

            # 调用LLM
            response = await generate_completion(
                messages=messages,
                max_tokens=10,
                temperature=0.1
            )

            # 解析响应
            response_text = ""
            if hasattr(response, 'choices') and len(response.choices) > 0:
                response_text = response.choices[0].message.content.strip()
            elif isinstance(response, dict) and 'choices' in response:
                response_text = response['choices'][0]['message']['content'].strip()
            else:
                self.logger.error(f"无法解析LLM响应格式: {type(response)}")
                return None

            self.logger.info(f"LLM识别响应: {response_text}")

            # 提取数字
            import re
            numbers = re.findall(r'\d+', response_text)
            if numbers:
                house_index = int(numbers[0]) - 1  # 转换为0基索引
                if 0 <= house_index < len(house_list):
                    return house_list[house_index]

            return None

        except Exception as e:
            self.logger.error(f"LLM调用失败: {str(e)}")
            return None

    def _fallback_identify_house(self, user_message: str, house_list: list) -> dict:
        """
        回退的房源识别方法，针对实际house_list结构优化

        Args:
            user_message: 用户消息
            house_list: 房源列表，格式：[{'resblock_id': '', 'house_id': '', 'name': ''}]

        Returns:
            识别到的房源对象，如果无法识别则返回None
        """
        import re

        # 1. 通过序号识别（优先级最高）
        patterns = [
            (r'第?一套|第?1套|1套', 0),
            (r'第?二套|第?2套|2套', 1),
            (r'第?三套|第?3套|3套', 2),
            (r'第?四套|第?4套|4套', 3),
            (r'第?五套|第?5套|5套', 4),
            (r'第?六套|第?6套|6套', 5),
            (r'第?七套|第?7套|7套', 6),
            (r'第?八套|第?8套|8套', 7),
            (r'第?九套|第?9套|9套', 8),
        ]

        for pattern, index in patterns:
            if re.search(pattern, user_message) and index < len(house_list):
                return house_list[index]

        # 2. 通过房源名称中的户型和卧室组合识别（优先级高）
        for house in house_list:
            name = house.get("name", "")
            if name:
                # 同时匹配户型和卧室编号
                if "2居室" in user_message and "01卧" in user_message:
                    if "2居室" in name and "01卧" in name:
                        return house
                elif "2居室" in user_message and "02卧" in user_message:
                    if "2居室" in name and "02卧" in name:
                        return house
                elif "3居室" in user_message and "01卧" in user_message:
                    if "3居室" in name and "01卧" in name:
                        return house
                elif "3居室" in user_message and "02卧" in user_message:
                    if "3居室" in name and "02卧" in name:
                        return house
                elif "3居室" in user_message and "03卧" in user_message:
                    if "3居室" in name and "03卧" in name:
                        return house
                elif "4居室" in user_message and "02卧" in user_message:
                    if "4居室" in name and "02卧" in name:
                        return house

        # 3. 通过房源名称中的户型识别（单独匹配）
        for house in house_list:
            name = house.get("name", "")
            if name:
                # 匹配户型：2居室、3居室、4居室等
                if "2居室" in user_message and "2居室" in name:
                    return house
                elif "3居室" in user_message and "3居室" in name:
                    return house
                elif "4居室" in user_message and "4居室" in name:
                    return house
                elif "1居室" in user_message and "1居室" in name:
                    return house

        # 4. 通过卧室编号识别
        for house in house_list:
            name = house.get("name", "")
            if name:
                # 匹配卧室编号：01卧、02卧、03卧等
                bedroom_patterns = [
                    (r'01卧|1卧|第一间|第1间', '01卧'),
                    (r'02卧|2卧|第二间|第2间', '02卧'),
                    (r'03卧|3卧|第三间|第3间', '03卧'),
                    (r'04卧|4卧|第四间|第4间', '04卧'),
                ]

                for pattern, bedroom_code in bedroom_patterns:
                    if re.search(pattern, user_message) and bedroom_code in name:
                        return house

        # 5. 通过房源名称关键词识别
        for house in house_list:
            name = house.get("name", "")
            if name:
                # 提取小区名称（去掉户型和卧室信息）
                name_parts = name.split('居室')[0] if '居室' in name else name.split('-')[0]
                if name_parts and name_parts in user_message:
                    return house

                # 检查小区名称的部分匹配
                if "大山子" in user_message and "大山子" in name:
                    return house
                elif "北里" in user_message and "北里" in name:
                    return house

        # 5. 通过数字序号识别（更灵活的匹配）
        numbers = re.findall(r'\d+', user_message)
        if numbers:
            try:
                # 尝试将第一个数字作为序号
                index = int(numbers[0]) - 1
                if 0 <= index < len(house_list):
                    return house_list[index]
            except (ValueError, IndexError):
                pass

        # 6. 通过指代词识别
        if any(word in user_message for word in ["最后", "最后一套", "最后那套"]):
            return house_list[-1]  # 返回最后一套
        elif any(word in user_message for word in ["这套", "那套", "上面那套", "上面的"]):
            return house_list[-1]  # 返回最后一套（默认指向最后展示的）
        elif any(word in user_message for word in ["第一", "首个", "开头", "第一个"]):
            return house_list[0]   # 返回第一套

        return None

    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤

        只有在意图为预约看房时才执行

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        # 检查意图是否为预约看房
        if not hasattr(context, 'intent') or not context.intent:
            return False

        # 意图识别返回映射后的字符串，判断是否为预约看房
        intent = context.intent
        is_viewing_appointment = (intent == "viewing_appointment")

        if not is_viewing_appointment:
            self.logger.debug(f"意图不是预约看房 ({intent})，跳过预约处理")
            return False

        self.logger.info("检测到预约看房意图，执行预约处理步骤")
        return True

    async def _create_error_response(self, context: WorkflowContext, response_text: str, session_state: str = "viewing_appointment_error") -> WorkflowContext:
        """
        创建错误响应的辅助方法

        Args:
            context: 工作流上下文
            response_text: 响应文本
            session_state: 会话状态

        Returns:
            更新了响应的上下文
        """
        # 创建统一的工作流响应
        context.workflow_response = create_text_response(
            content=response_text,
            session_state=session_state,
            delay=0.05,
            requires_typewriter=True
        )

        # 保持向后兼容
        context.response = response_text
        await self.session_service.add_message(
            session_id=context.session_id,
            role="assistant",
            content=response_text
        )
        context.early_return = True
        return context

    def _build_viewing_sse_response(
        self, 
        reservation_result: dict, 
        viewing_time: str = "", 
        parsed_datetime=None, 
        display_time: str = ""
    ) -> dict:
        """
        构建看房单的SSE格式响应数据
        
        Args:
            reservation_result: 预约接口返回的结果
            viewing_time: 原始时间文本
            parsed_datetime: 解析后的datetime对象
            display_time: 用户友好的显示时间
            
        Returns:
            SSE格式的响应数据，包含parent_type为view_house的多个数据块
        """
        sse_data = {
            "parent_type": "view_house",
            "data": []
        }
        
        # 1. 添加看房单图片 (sub_type: view_house_img)
        sse_data["data"].append({
            "sub_type": "view_house_img",
            "content": {
                "img": "https://rent-cdn.ziroom.com/007qm/483dfbcdefec439e868fb99ab77ba76f.png"
            }
        })
        
        # 2. 添加看房单信息 (sub_type: view_house_info)，包含预约时间
        view_time_info = {
            "view_time": parsed_datetime.strftime("%Y-%m-%d %H:%M:%S") if parsed_datetime else ""
        }
        
        sse_data["data"].append({
            "sub_type": "view_house_info",
            "content": view_time_info
        })
        
        # 3. 提取房源列表数据 (sub_type: view_house_list)
        house_info = reservation_result.get("houseInfo", {})
        house_list_data = []
        
        # 从 houseInfo.villageList[].buildingsList[].houseList 中提取房源数据
        village_list = house_info.get("villageList", [])
        for village in village_list:
            buildings_list = village.get("buildingsList", [])
            for building in buildings_list:
                house_list = building.get("houseList", [])
                house_list_data.extend(house_list)
        
        # 只有当有房源数据时才添加
        if house_list_data:
            sse_data["data"].append({
                "sub_type": "view_house_list",
                "content": house_list_data
            })
        
        # 4. 提取管家信息数据 (sub_type: view_house_keeper)
        keeper_info = reservation_result.get("keeperInfo", {})
        sse_data["data"].append({
            "sub_type": "view_house_keeper", 
            "content": keeper_info
        })
        
        # 5. 添加更多信息数据 (sub_type: view_house_more) - 空白字符
        sse_data["data"].append({
            "sub_type": "view_house_more",
            "content": " "
        })
        
        self.logger.info(f"构建SSE看房单响应数据：包含{len(house_list_data)}套房源，管家：{keeper_info.get('keeperName', '未知')}")
        return sse_data

    def _convert_house_list_for_reservation(self, context: dict) -> list:
        """
        将house_list转换成预约接口需要的格式

        Args:
            context: 上下文

        Returns:
            转换后的house_list
        """
        # 尝试从多个位置获取house_list
        house_list_raw = []
        
        # 优先从当前上下文获取
        if "house_list" in context:
            house_list_raw = context["house_list"]
            self.logger.info(f"host_list_raw: {house_list_raw}")
            self.logger.info(f"从current_context获取house_list: 共{len(house_list_raw)}套房源")
        else:
            self.logger.warning("未找到house_list数据")
        
        house_list = []
        for house in house_list_raw:
            resblock_id = house.get("resblock_id")
            house_id = house.get("house_id")  # 使用house_id字段
            
            if resblock_id and house_id:
                # 转换为整数类型（如果是字符串的话）
                try:
                    resblock_id_int = int(resblock_id)
                    house_id_int = int(house_id)
                    house_list.append({
                        "resblockId": resblock_id_int,
                        "houseId": house_id_int
                    })
                except ValueError as e:
                    self.logger.warning(f"转换房源ID失败: resblock_id={resblock_id}, house_id={house_id}, error={e}")
            else:
                self.logger.warning(f"房源数据缺少必要字段: resblock_id={resblock_id}, house_id={house_id}")
        
        self.logger.info(f"转换后的house_list: {house_list}")
        
        return house_list 