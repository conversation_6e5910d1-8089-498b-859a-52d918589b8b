"""
询问参数步骤模块

提供询问用户特定参数的工作流步骤。
"""

import logging
from typing import Dict, Callable

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.services.session_service import SessionService
from app.services.context_service import SessionState
from app.core.prompt_config import get_parameter_config, get_response_text

logger = logging.getLogger(__name__)


class AskParameterStep(WorkflowStep):
    """
    询问参数步骤

    统一处理询问位置、预算和户型等参数的步骤。
    根据参数类型生成不同的询问内容。
    使用统一的配置，避免重复定义。
    """

    def __init__(self, session_service: SessionService, param_type: str):
        """
        初始化询问参数步骤

        Args:
            session_service: 会话服务，用于更新会话上下文
            param_type: 参数类型，如 "location"、"budget" 或 "room_type"
        """
        super().__init__(f"询问{param_type}")
        self.session_service = session_service
        self.param_type = param_type
        self.config = get_parameter_config(param_type)

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        生成询问参数的响应

        Args:
            context: 工作流上下文

        Returns:
            更新了响应的上下文
        """
        # 获取配置
        prompt_version = self.config.get("prompt_version", f"ask_{self.param_type}")
        question = get_response_text(prompt_version) or self.config.get("question", f"请提供{self.param_type}信息")
        context_key = self.config.get("context_key", f"{self.param_type}_asked")
        session_state = self.config.get("session_state", SessionState.PROCESSING)

        # 从上下文中获取必要信息
        session_id = context.session_id
        
        # 设置提示版本
        context.prompt_version = prompt_version
        # 设置响应
        context.response = question

        # 更新会话上下文
        await self.session_service.update_session_context(
            session_id, {context_key: 1, "session_state": session_state}
        )

        # 保存消息到数据库
        if context.response:
            await self.session_service.add_message(
                session_id=session_id,
                role="assistant",
                content=context.response
            )
            self.logger.info(f"✅ AskParameterStep：已保存assistant消息到数据库")

        # 设置提前返回标志，避免执行后续步骤
        context.early_return = True

        return context

    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        # 只有搜索房源意图才询问参数
        if context.intent != "search_house":
            return False

        # 检查是否已经询问过（每个参数最多问一次）
        if self._already_asked(context):
            return False

        # 检查当前消息是否已提取到有效参数
        if self._has_valid_extracted_param(context):
            return False

        # 检查是否满足询问条件
        return self._should_ask_param(context)

    def _already_asked(self, context: WorkflowContext) -> bool:
        """检查是否已经询问过该参数"""
        asked_key = f"{self.param_type}_asked"
        
        # 从updated_context获取询问状态（UpdateContextStep已确保包含完整会话状态）
        session_context = getattr(context, 'updated_context', {})
        already_asked = session_context.get(asked_key, 0) > 0
        
        if already_asked:
            self.logger.info(f"参数 {self.param_type} 已经询问过，跳过重复询问")
        else:
            self.logger.info(f"参数 {self.param_type} 未询问过，询问状态: {session_context.get(asked_key, 0)}")
            
        return already_asked

    def _has_valid_extracted_param(self, context: WorkflowContext) -> bool:
        """检查当前消息是否已提取到有效参数"""
        extracted_params = getattr(context, 'extracted_params', {})
        
        if self.param_type not in extracted_params:
            return False
            
        param_value = extracted_params[self.param_type]
        is_valid = self._is_valid_param_value(param_value)
        
        if is_valid:
            self.logger.info(f"当前消息已提取到{self.param_type}参数: {param_value}, 跳过询问")
            
        return is_valid

    def _is_valid_param_value(self, value) -> bool:
        """检查参数值是否有效"""
        return (value is not None and 
                value != "" and 
                value != "null" and 
                str(value).strip() != "")

    def _should_ask_param(self, context: WorkflowContext) -> bool:
        """根据参数类型和上下文状态判断是否应该询问"""
        # 参数询问条件配置 - 每个参数完全独立
        ask_conditions: Dict[str, Callable[[WorkflowContext], bool]] = {
            "location": lambda ctx: not ctx.has_location,
            "budget": lambda ctx: not ctx.has_budget,
            "room_type": lambda ctx: not ctx.has_room_type
        }
        
        condition_func = ask_conditions.get(self.param_type)
        if not condition_func:
            return False
            
        should_ask = condition_func(context)
        
        self.logger.info(f"检查询问{self.param_type}: 位置={context.has_location}, "
                        f"预算={context.has_budget}, 户型={context.has_room_type}, "
                        f"需要询问={should_ask}")
        
        return should_ask
