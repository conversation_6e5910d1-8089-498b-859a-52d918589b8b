"""
生成响应步骤模块

提供生成各种类型响应的工作流步骤。
"""

import json
import logging
from typing import Dict, List, Optional

from app.core.prompt_config import PromptType
from app.llm.response import ResponseGenerator
from app.services.house_service import HouseService
from app.services.session_service import SessionService
from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.workflows.response_types import create_house_list_response, create_text_response

logger = logging.getLogger(__name__)


class GenerateResponseStep(WorkflowStep):
    """
    生成响应步骤

    统一处理生成各种类型的响应，包括房源响应和通用响应。
    根据上下文状态决定生成什么类型的响应。
    """

    def __init__(self, response_generator: ResponseGenerator, house_service: Optional[HouseService] = None, session_service: Optional[SessionService] = None):
        """
        初始化生成响应步骤

        Args:
            response_generator: 响应生成器，用于生成回复
            house_service: 房源服务，用于格式化房源信息（可选）
            session_service: 会话服务，用于更新会话状态（可选）
        """
        super().__init__("生成响应")
        self.response_generator = response_generator
        self.house_service = house_service
        self.session_service = session_service

    def _create_simplified_history(self, history: List[Dict[str, str]], context: WorkflowContext) -> List[Dict[str, str]]:
        """
        创建简化的对话历史，只保留用户的第一条消息

        Args:
            history: 原始对话历史
            context: 当前上下文

        Returns:
            简化后的对话历史
        """
        # 创建一个新的历史列表，只包含一条用户消息
        simplified_history = []

        # 创建一条包含所有搜索条件的用户消息
        location = context.updated_context.get("location", "")
        budget = context.updated_context.get("budget", "")
        room_type = context.updated_context.get("room_type", "")

        user_msg = "我想"
        if location:
            user_msg += f"在{location}"
        user_msg += "找房"
        if budget:
            if "," in budget:
                min_price, max_price = budget.split(",")
                user_msg += f"，预算{min_price}-{max_price}元"
            else:
                user_msg += f"，预算{budget}元"
        if room_type:
            user_msg += f"，{room_type}"

        # 添加用户消息
        simplified_history.append({
            "role": "user",
            "content": user_msg
        })

        return simplified_history

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        生成响应

        根据上下文状态生成不同类型的响应。

        Args:
            context: 工作流上下文

        Returns:
            更新了响应的上下文
        """
        # 从上下文中获取必要信息
        updated_context = context.updated_context
        search_result = context.search_result
        intent = context.intent

        # 只处理搜索房源相关的响应
        if intent == "search_house" or intent == "continue_search":
            # 检查是否有搜索结果
            if not search_result or (isinstance(search_result, list) and len(search_result) == 0):
                # 没有搜索结果，返回无房源消息
                no_result_message = "抱歉，在您指定的条件下没有找到合适的房源。建议您适当放宽搜索条件，比如扩大预算范围或选择更多区域。"

                # 创建统一的工作流响应
                context.workflow_response = create_text_response(
                    content=no_result_message,
                    session_state="browsing",
                    delay=0.05,
                    requires_typewriter=True
                )

                # 保持向后兼容
                context.response = no_result_message
                self.logger.info("搜索结果为空，返回无房源消息")
                return context
            
            # 有搜索结果，格式化房源信息
            # 确保会话状态为browsing
            if self.session_service and context.session_id:
                from app.services.context_service import SessionState
                # 记录当前会话状态
                self.logger.info(f"生成响应前的会话状态: {updated_context.get('session_state', 'unknown')}")

                # 更新会话状态为browsing
                try:
                    await self.session_service.update_session_context(
                        context.session_id, {"session_state": SessionState.BROWSING}
                    )
                    self.logger.info(f"在生成响应步骤中更新会话状态为browsing")
                except Exception as e:
                    self.logger.error(f"更新会话状态失败: {str(e)}")

            # 从updated_context中提取最新的参数，构建上下文字典
            context_dict = {
                "location": updated_context.get("location", ""),
                "location_type": updated_context.get("location_type", ""),
                "budget": updated_context.get("budget", ""),
                "room_type": updated_context.get("room_type", ""),
                "bed_room": updated_context.get("bed_room", "")
            }

            # 使用房源服务格式化房源信息为JSON格式
            houses_json = self.house_service.format_houses_json(search_result, context_dict) if self.house_service else {"title": "看房单", "data": []}
            self.logger.info(f"房源JSON信息: {houses_json}")

            # 获取位置类型
            location_type = updated_context.get("location_type", "")

            # 创建统一的工作流响应
            context.workflow_response = create_house_list_response(
                houses_data=houses_json,
                location_type=location_type,
                session_state="browsing"
            )

            # 保持向后兼容
            context.response = json.dumps(houses_json, ensure_ascii=False)

            self.logger.info(f"创建房源列表响应，位置类型: {location_type}")

        return context
